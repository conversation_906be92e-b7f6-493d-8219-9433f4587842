<?php
include "htmlhead.php";
$dh = "home";
include("conn.php");
include("checkvip.php");
include("zysx_x_functions.php"); // 引入公共函数

// 获取筛选条件
$gxlb = isset($_GET['gxlb']) ? $_GET['gxlb'] : '985';

// 定义不同类别的筛选条件 - 根据yxinfo表字段修改
$categoryConditions = [
    '985' => [
        'titlebarnr' => "985高校",
        'pc' => "`批次` = '本科'",
        'if_jh' => "`f985` = 1"
    ],
    '211' => [
        'titlebarnr' => "211",
        'pc' => "`批次` = '本科'",
        'if_jh' => "`f211` = 1 AND (`f985` != 1 OR `f985` IS NULL)"
    ],
    'syl' => [
        'titlebarnr' => "双一流建设",
        'pc' => "`批次` = '本科'",
        'if_jh' => "`双一流` IS NOT NULL AND `双一流` != ''"
    ],
    'sfyx' => [
        'titlebarnr' => "双非高校列表",
        'pc' => "`批次` = '本科'",
        'if_jh' => "(`f211` != 1 OR `f211` IS NULL) AND (`f985` != 1 OR `f985` IS NULL)"
    ],
    'bwzs' => [
        'titlebarnr' => "中央部委直属高校",
        'pc' => "`批次` = '本科'",
        'if_jh' => "`中央部属` = '1'"
    ],
    'gzsg' => [
        'titlebarnr' => "双高高职院校",
        'pc' => "`批次` = '专科'",
        'if_jh' => "`双高计划` IS NOT NULL AND `双高计划` != '' AND `双高计划` != '0'"
    ],
    'sfgz' => [
        'titlebarnr' => "国家示范高职院校",
        'pc' => "`批次`='专科'",
        'if_jh' => "`学院层次` LIKE '%76001%'"
    ],
    'gggz' => [
        'titlebarnr' => "国家级骨干",
        'pc' => "`批次`='专科'",
        'if_jh' => "`学院层次` LIKE '%76002%'"
    ],
    'sngz' => [
        'titlebarnr' => "江西省高职院校",
        'pc' => "`批次`='专科'",
        'if_jh' => "`省份名称` = '江西'"
    ],
    'snbk' => [
        'titlebarnr' => "江西省本科院校",
        'pc' => "`批次`='本科'",
        'if_jh' => "`省份名称` = '江西'"
    ],
    'tqbk' => [
        'titlebarnr' => "提前本科院校",
        'pc' => "`批次`='提前本科'",
        'if_jh' => "1=1"
    ],
    'zzsg' => [
        'titlebarnr' => "定向培养军士",
        'pc' => "`批次`='提前专科'",
        'if_jh' => "`备注` LIKE '%军士%'"
    ],
    'sflbk' => [
        'titlebarnr' => "师范类本科",
        'pc' => "`批次`='本科'",
        'if_jh' => "`类型` LIKE '%师范%'"
    ],
    'yxlbk' => [
        'titlebarnr' => "医学类本科",
        'pc' => "`批次`='本科'",
        'if_jh' => "`类型` LIKE '%医%'"
    ],
    'cjlbk' => [
        'titlebarnr' => "财经类本科",
        'pc' => "`批次`='本科'",
        'if_jh' => "`类型` LIKE '%财%'"
    ],
    'hkyxbk' => [
        'titlebarnr' => "航空类院校",
        'pc' => "`批次`='本科'",
        'if_jh' => "`院校名称` LIKE '%航%'"
    ],
    'zflbk' => [
        'titlebarnr' => "政法类本科",
        'pc' => "`批次`='本科'",
        'if_jh' => "`类型` LIKE '%政法%'"
    ],
    'yylbk' => [
        'titlebarnr' => "语言类本科",
        'pc' => "`批次`='本科'",
        'if_jh' => "`类型` LIKE '%语言%'"
    ],
    'sflzk' => [
        'titlebarnr' => "师范类专科",
        'pc' => "`批次`='专科'",
        'if_jh' => "`院校名称` LIKE '%师范%' OR `主管部门` LIKE '%师范%'"
    ],
    'yxlzk' => [
        'titlebarnr' => "医学类专科",
        'pc' => "`批次`='专科'",
        'if_jh' => "`院校名称` LIKE '%医%' OR `主管部门` LIKE '%医%'"
    ],
    'cjlzk' => [
        'titlebarnr' => "财经类专科",
        'pc' => "`批次`='专科'",
        'if_jh' => "`院校名称` LIKE '%财%' OR `主管部门` LIKE '%财%'"
    ],
    'hkyxzk' => [
        'titlebarnr' => "航空类院校",
        'pc' => "`批次` = '专科'",
        'if_jh' => "`院校名称` LIKE '%航%'"
    ],
    'zflzk' => [
        'titlebarnr' => "政法类专科",
        'pc' => "`批次`='专科'",
        'if_jh' => "`类型` LIKE '%政法%'"
    ],
    'jzqx' => [
        'titlebarnr' => "建筑新、老八校",
        'pc' => "`批次`='本科'",
        'if_jh' => "`院校名称` = '清华大学' or `院校名称`  = '东南大学'  or `院校名称` = '天津大学' or `院校名称` = '同济大学' or `院校名称` = '哈尔滨工业大学' or `院校名称` = '华南理工大学' or `院校名称` = '重庆大学' or `院校名称` = '西安建筑科技大学' or `院校名称` = '华中科技大学' or `院校名称`  = '浙江大学'  or `院校名称` = '湖南大学' or `院校名称` = '沈阳建筑大学' or `院校名称` = '南京大学' or `院校名称` = '大连理工大学' or `院校名称` = '上海交通大学' or `院校名称` = '深圳大学'"
    ],
    'jxqx' => [
        'titlebarnr' => "机械强校",
        'pc' => "`批次`='本科'",
        'if_jh' => "`院校名称` = '清华大学' or `院校名称`  = '上海交通大学'  or `院校名称` = '华中科技大学' or `院校名称` = '西安交通大学' or `院校名称` = '哈尔滨工业大学' or `院校名称` = '吉林大学' or `院校名称` = '湖南大学' or `院校名称` = '燕山大学' or `院校名称` = '合肥工业大学'"
    ],
    'dzdq' => [
        'titlebarnr' => "电子、电气",
        'pc' => "`批次`='本科'",
        'if_jh' => "`院校名称` = '电子科技大学' or `院校名称` = '西安电子科技大学'  or `院校名称` = '北京邮电大学' or `院校名称` = '清华大学' or `院校名称` = '华中科技大学' or `院校名称` = '西安交通大学' or `院校名称` = '浙江大学' or `院校名称` = '华北电力大学' or `院校名称` = '武汉大学' "
    ],
    'zwhz' => [
        'titlebarnr' => "中外合作",
        'pc' => "`批次` like '%本科%'",
        'if_jh' => "`办学性质` LIKE '%中外%' OR `办学性质` LIKE '%合作%'"
    ],
    'gxzx' => [
        'titlebarnr' => "高校专项",
        'pc' => "`批次`='本科'",
        'if_jh' => "`院校名称` = '北京大学'  or `院校名称` = '清华大学'  or `院校名称` = '中国人民大学'  or `院校名称` = '北京交通大学'  or `院校名称` = '北京科技大学'  or `院校名称` = '北京化工大学'  or `院校名称` = '北京邮电大学'  or `院校名称` = '中国农业大学'  or `院校名称` = '中国政法大学'  or `院校名称` = '华北电力大学'  or `院校名称` = '北京林业大学'  or `院校名称` = '北京中医药大学'  or `院校名称` = '北京师范大学'  or `院校名称` = '北京外国语大学'  or `院校名称` = '北京语言大学'  or `院校名称` = '中国传媒大学'  or `院校名称` = '中央财经大学'  or `院校名称` = '对外经济贸易大学'  or `院校名称` = '中国矿业大学'  or `院校名称` = '中国石油大学'  or `院校名称` = '中国地质大学'  or `院校名称` = '南开大学'  or `院校名称` = '天津大学'  or `院校名称` = '复旦大学'  or `院校名称` = '同济大学'  or `院校名称` = '上海交通大学'  or `院校名称` = '华东理工大学'  or `院校名称` = '东华大学'  or `院校名称` = '华东师范大学'  or `院校名称` = '上海外国语大学'  or `院校名称` = '上海财经大学'  or `院校名称` = '南京大学'  or `院校名称` = '东南大学'  or `院校名称` = '中国矿业大学'  or `院校名称` = '河海大学'  or `院校名称` = '江南大学'  or `院校名称` = '南京农业大学'  or `院校名称` = '中国药科大学'  or `院校名称` = '浙江大学'  or `院校名称` = '合肥工业大学'  or `院校名称` = '厦门大学'  or `院校名称` = '山东大学'  or `院校名称` = '中国海洋大学'  or `院校名称` = '大连理工大学'  or `院校名称` = '东北大学'  or `院校名称` = '吉林大学'  or `院校名称` = '东北师范大学'  or `院校名称` = '东北林业大学'  or `院校名称` = '武汉大学'  or `院校名称` = '华中科技大学'  or `院校名称` = '武汉理工大学'  or `院校名称` = '华中农业大学'  or `院校名称` = '华中师范大学'  or `院校名称` = '中南财经政法大学'  or `院校名称` = '湖南大学'  or `院校名称` = '中南大学'  or `院校名称` = '中山大学'  or `院校名称` = '华南理工大学'  or `院校名称` = '重庆大学'  or `院校名称` = '西南大学'  or `院校名称` = '四川大学'  or `院校名称` = '西南交通大学'  or `院校名称` = '电子科技大学'  or `院校名称` = '西南财经大学'  or `院校名称` = '西安交通大学'  or `院校名称` = '西安电子科技大学'  or `院校名称` = '长安大学'  or `院校名称` = '西北农林科技大学'  or `院校名称` = '陕西师范大学'  or `院校名称` = '兰州大学'  or `院校名称` = '北京航空航天大学'  or `院校名称` = '北京理工大学'  or `院校名称` = '哈尔滨工业大学'  or `院校名称` = '哈尔滨工程大学'  or `院校名称` = '南京航空航天大学'  or `院校名称` = '南京理工大学'  or `院校名称` = '西北工业大学'  or `院校名称` = '大连海事大学'  or `院校名称` = '中国科学'  or `院校名称` = '技术大学'  or `院校名称` = '北京工业大学'  or `院校名称` = '黑龙江大学'  or `院校名称` = '上海大学'  or `院校名称` = '苏州大学'  or `院校名称` = '南京师范大学'  or `院校名称` = '福州大学'  or `院校名称` = '郑州大学'  or `院校名称` = '湖南师范大学'  or `院校名称` = '广西大学'  or `院校名称` = '西南政法大学'  or `院校名称` = '四川农业大学'  or `院校名称` = '贵州大学'  or `院校名称` = '云南大学'  or `院校名称` = '西北大学'"
    ],
    'gzdz' => [
        'titlebarnr' => "高职单招",
        'pc' => "`批次`='专科'",
        'if_jh' => "`院校名称` = '江西科技学院' or `院校名称` = '南昌理工学院' or `院校名称` = '南昌工学院' or `院校名称` = '江西应用科技学院' or `院校名称` = '江西软件职业技术大学' or `院校名称` = '南昌交通学院' or `院校名称` = '景德镇艺术职业大学' or `院校名称` = '南昌大学共青学院' or `院校名称` = '江西医学高等专科学校' or `院校名称` = '宜春幼儿师范高等专科学校' or `院校名称` = '上饶幼儿师范高等专科学校' or `院校名称` = '抚州幼儿师范高等专科学校' or `院校名称` = '九江职业大学' or `院校名称` = '江西职业技术大学' or `院校名称` = '江西工业职业技术学院' or `院校名称` = '江西电力职业技术学院' or `院校名称` = '江西旅游商贸职业学院' or `院校名称` = '江西机电职业技术学院' or `院校名称` = '江西陶瓷工艺美术职业技术学院' or `院校名称` = '江西环境工程职业学院' or `院校名称` = '江西信息应用职业技术学院' or `院校名称` = '江西工业工程职业技术学院' or `院校名称` = '江西交通职业技术学院' or `院校名称` = '江西艺术职业学院' or `院校名称` = '江西财经职业学院' or `院校名称` = '江西司法警官职业学院' or `院校名称` = '江西应用技术职业学院' or `院校名称` = '鹰潭职业技术学院' or `院校名称` = '江西现代职业技术学院' or `院校名称` = '江西外语外贸职业学院' or `院校名称` = '江西工业贸易职业学院' or `院校名称` = '江西应用工程职业学院' or `院校名称` = '江西建设职业技术学院' or `院校名称` = '宜春职业技术学院' or `院校名称` = '抚州职业技术学院' or `院校名称` = '江西生物科技职业学院' or `院校名称` = '江西青年职业学院' or `院校名称` = '上饶职业技术学院' or `院校名称` = '江西农业工程职业学院' or `院校名称` = '江西科技职业学院' or `院校名称` = '江西航空职业技术学院' or `院校名称` = '赣西科技职业学院' or `院校名称` = '江西制造职业技术学院' or `院校名称` = '江西工程职业学院' or `院校名称` = '江西泰豪动漫职业学院' or `院校名称` = '江西枫林涉外经贸职业学院' or `院校名称` = '江西传媒职业学院' or `院校名称` = '江西冶金职业技术学院' or `院校名称` = '江西新能源科技职业学院' or `院校名称` = '江西工商职业技术学院' or `院校名称` = '景德镇陶瓷职业技术学院' or `院校名称` = '共青科技职业学院' or `院校名称` = '江西水利职业学院' or `院校名称` = '吉安职业技术学院' or `院校名称` = '江西洪州职业学院' or `院校名称` = '南昌影视传播职业学院' or `院校名称` = '赣南卫生健康职业学院' or `院校名称` = '萍乡卫生职业学院' or `院校名称` = '江西婺源茶业职业学院' or `院校名称` = '赣州职业技术学院' or `院校名称` = '南昌健康职业技术学院' or `院校名称` = '九江理工职业学院' or `院校名称` = '和君职业学院' or `院校名称` = '江西经济管理干部学院' or `院校名称` = '北京社会管理职业学院' or `院校名称` = '铁门关职业技术学院'"
    ],
    'zsb' => [
        'titlebarnr' => "专升本院校",
        'pc' => "`批次`='专科'",
        'if_jh' => "`院校名称` = '南昌大学共青学院' or `院校名称` = '景德镇艺术职业大学' or `院校名称` = '江西服装学院' or `院校名称` = '江西应用科技学院' or `院校名称` = '赣南医学院' or `院校名称` = '赣南师范大学' or `院校名称` = '宜春学院' or `院校名称` = '南昌大学科学技术学院' or `院校名称` = '江西科技学院' or `院校名称` = '南昌交通学院' or `院校名称` = '江西工程学院' or `院校名称` = '华东交通大学' or `院校名称` = '赣南科技学院' or `院校名称` = '江西理工大学' or `院校名称` = '南昌师范学院' or `院校名称` = '景德镇陶瓷大学' or `院校名称` = '江西农业大学南昌商学院' or `院校名称` = '新余学院' or `院校名称` = '南昌医学院' or `院校名称` = '九江学院' or `院校名称` = '豫章师范学院' or `院校名称` = '南昌航空大学科技学院' or `院校名称` = '赣南师范大学科技学院' or `院校名称` = '南昌航空大学' or `院校名称` = '井冈山大学' or `院校名称` = '南昌工程学院' or `院校名称` = '萍乡学院' or `院校名称` = '江西软件职业技术大学' or `院校名称` = '南昌工学院' or `院校名称` = '上饶师范学院' or `院校名称` = '江西财经大学' or `院校名称` = '南昌职业大学' or `院校名称` = '南昌应用技术师范学院' or `院校名称` = '东华理工大学' or `院校名称` = '江西中医药大学' or `院校名称` = '江西财经大学现代经济管理学院' or `院校名称` = '江西师范大学科学技术学院' or `院校名称` = '景德镇学院' or `院校名称` = '南昌理工学院' or `院校名称` = '江西农业大学' or `院校名称` = '江西科技师范大学'"
    ],
    'dx_gjgf' => [
        'titlebarnr' => "国家公费师范生",
        'pc' => "`批次`='提前本科'",
        'if_jh' => "`院校代号`= '0221' or `院校代号` = '0282' or `院校代号` = '0541' or `院校代号` = '0668' or `院校代号` = '0772' or `院校代号` ='0871'"
    ],
    'dx_dfmf' => [
        'titlebarnr' => "地方免费师范生",
        'pc' => "`批次`='提前本科'",
        'if_jh' => "`院校名称` = '江西师范大学' or `院校名称` = '赣南师范大学' or `院校名称` = '江西科技师范大学'"
    ],
    'dx_gjmfyxs' => [
        'titlebarnr' => "医学定向",
        'pc' => "`批次` like '%提前%'",
        'if_jh' => "`院校名称` = '赣南医科大学' or `院校名称` = '江西中医药大学' or `院校名称` = '赣南卫生健康职业学院' or `院校名称` = '南昌大学' or `院校名称` = '南昌医学院'"
    ],
    'dx_qt' => [
        'titlebarnr' => "其他定向计划",
        'pc' => "`批次` = '提前本科'",
        'if_jh' => "`院校名称` = '陆军工程大学' or `院校名称` = '新疆警察学院'  or `院校名称` = '成都信息工程大学'  or `院校名称` = '南京信息工程大学'  or `院校名称` = '清华大学'"
    ],
    'dx_zk' => [
        'titlebarnr' => "专科定向计划",
        'pc' => "`批次`= '提前专科'",
        'if_jh' => "`院校名称` like '%石家庄邮电%' or `院校名称` like '%赣南卫生%'"
    ],
    'c9' => [
        'titlebarnr' => "C9联盟",
        'pc' => "`批次` = '本科'",
        'if_jh' => "`院校名称` = '清华大学' or `院校名称` = '北京大学'  or `院校名称` = '复旦大学'  or `院校名称` = '南京大学'  or `院校名称` = '浙江大学'  or `院校名称` = '上海交通大学'  or `院校名称` = '西安交通大学'  or `院校名称` = '哈尔滨工业大学'  or `院校名称` = '中国科学技术大学'"
    ],
    'e9' => [
        'titlebarnr' => "E9联盟",
        'pc' => "`批次` = '本科'",
        'if_jh' => "`院校名称` = '北京理工大学' or `院校名称` = '重庆大学'  or `院校名称` = '大连理工大学'  or `院校名称` = '东南大学'  or `院校名称` = '哈尔滨工业大学'  or `院校名称` = '华南理工大学'  or `院校名称` = '天津大学'  or `院校名称` = '同济大学'  or `院校名称` = '西北工业大学'"
    ],
    'gf7z' => [
        'titlebarnr' => "国防七子",
        'pc' => "`批次` = '本科'",
        'if_jh' => "`院校名称` = '北京理工大学' or `院校名称` = '北京航空航天大学'  or `院校名称` = '哈尔滨工程大学'  or `院校名称` = '南京航空航天大学'  or `院校名称` = '哈尔滨工业大学' or `院校名称` = '西北工业大学' or `院校名称` = '南京理工大学'"
    ],
    'bg7z' => [
        'titlebarnr' => "兵工七子",
        'pc' => "`批次` = '本科'",
        'if_jh' => "`院校名称` = '北京理工大学' or `院校名称` = '南京理工大学'  or `院校名称` = '中北大学'  or `院校名称` = '长春理工大学'  or `院校名称` = '沈阳理工大学' or `院校名称` = '西安工业大学' or `院校名称` = '重庆理工大学'"
    ],
    'dll6x' => [
        'titlebarnr' => "电力老6校",
        'pc' => "`批次` = '本科'",
        'if_jh' => "`院校名称` = '长沙理工大学' or `院校名称` = '上海电力大学'  or `院校名称` = '东北电力大学'  or `院校名称` = '三峡大学'  or `院校名称` = '南京工程大学' or `院校名称` = '沈阳工程学院'"
    ],
    'dq2l4h' => [
        'titlebarnr' => "电气2龙4虎",
        'pc' => "`批次` = '本科'",
        'if_jh' => "`院校名称` = '武汉大学' or `院校名称` = '华北电力大学'  or `院校名称` = '清华大学'  or `院校名称` = '浙江大学'  or `院校名称` = '西安交通大学' or `院校名称` = '华中科技大学'"
    ],
    'dx6z' => [
        'titlebarnr' => "电信六子",
        'pc' => "`批次` = '本科'",
        'if_jh' => "`院校名称` = '北京邮电大学' or `院校名称` = '电子科技大学'  or `院校名称` = '西安电子科技大学'  or `院校名称` = '桂林电子科技大学'  or `院校名称` = '杭州电子科技大学' or `院校名称` = '北京信息科技大学'"
    ],
    'wy4x' => [
        'titlebarnr' => "五院四系",
        'pc' => "`批次` = '本科'",
        'if_jh' => "`院校名称` = '中国政法大学' or `院校名称` = '西南政法大学'  or `院校名称` = '华东政法大学'  or `院校名称` = '西北政法大学'  or `院校名称` = '中南财经政法大学' or `院校名称` = '北京大学' or `院校名称` = '武汉大学' or `院校名称` = '吉林大学' or `院校名称` = '中国人民大学'"
    ],
    'jslyx' => [
        'titlebarnr' => "军事类院校",
        'pc' => "`批次` = '提前本科'",
        'if_jh' => "`备注` like '%军、警类%'"
    ]

];


$titlebarnr = $categoryConditions[$gxlb]['titlebarnr'];
$pc = $categoryConditions[$gxlb]['pc'];
$if_jh = $categoryConditions[$gxlb]['if_jh'];

// 获取当前年份对应的表名
$tdxk_nf = date("Y");
$tdxk_mc = 'tdx' . $tdxk_nf;
while (!(mysqli_num_rows(mysqli_query($conn, "SHOW TABLES LIKE '" . $tdxk_mc . "'")) == 1)) {
    $tdxk_nf = $tdxk_nf - 1;
    $tdxk_mc = 'tdx' . $tdxk_nf;
}

// 替换 if_jh 中的列名，指定所属表
$if_jh = str_replace('`院校名称`', "`$tdxk_mc`.`院校名称`", $if_jh);
$if_jh = str_replace('`院校代号`', "`$tdxk_mc`.`院校代号`", $if_jh);
$if_jh = str_replace('`专业组名称`', "`$tdxk_mc`.`专业组名称`", $if_jh);
$if_jh = str_replace('`类型`', "`$tdxk_mc`.`类型`", $if_jh);
$if_jh = str_replace('`备注`', "`$tdxk_mc`.`备注`", $if_jh);
$if_jh = str_replace('`TDXK_TABLE`', "`$tdxk_mc`", $if_jh);
$if_jh = str_replace('TDXK_TABLE', "`$tdxk_mc`", $if_jh);

// 调试信息 - 可以临时启用来查看实际的筛选条件
// echo "<!-- Debug: gxlb=$gxlb, if_jh=$if_jh, tdxk_mc=$tdxk_mc -->";

// 明确指定批次列所属的表
$pc = "`$tdxk_mc`.$pc";

// 生成 zsjh 表名
$zsjh_mc = 'zsjh' . $tdxk_nf;

// 特殊科类数组
$specialCategories = ["美术", "音乐", "舞蹈", "播音", "表", "三校生", "书法", "体育"];
foreach ($specialCategories as $specialCategory) {
    if (strpos($gxlb, $specialCategory) !== false) {
        $titlebarnr = $gxlb;
        break;
    }
}

include("titlebar.php");

// 引入收藏功能样式
echo '<link rel="stylesheet" href="css/word_sc.css">';

// 获取当前选择的科类，默认为第一个科类
$currentCategory = isset($_GET['category']) ? $_GET['category'] : '';

// 分页参数
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$limit = 10;
$start = ($page - 1) * $limit;

// 特殊科类数组
$specialCategories = ["美术", "音乐", "舞蹈", "播音", "表", "三校生", "书法", "体育"];
$showAllCategories = false;
foreach ($specialCategories as $specialCategory) {
    if (strpos($gxlb, $specialCategory) !== false) {
        $showAllCategories = true;
        break;
    }
}

if ($showAllCategories) {
    // 以批次分组获取标签，调整排序为升序，并添加科类条件
    $categorySql = "SELECT DISTINCT `批次` as 科类 FROM `$tdxk_mc` WHERE `科类` = '$gxlb' ORDER BY FIELD(批次, '提前本科', '本科', '提前专科', '专科') ASC";
    $bkkllink = "&bkkl=$gxlb";
} else {
    // 正常获取科类标签
    $categorySql = "SELECT DISTINCT `科类` FROM `$tdxk_mc` WHERE $pc AND EXISTS (SELECT 1 FROM `yxinfo` WHERE `yxinfo`.`院校名称` = `$tdxk_mc`.`院校名称` AND ($if_jh)) ORDER BY FIELD(科类, '三校生', '历史类', '物理类') DESC";
    $bkkllink = "";
}

$categoryResult = mysqli_query($conn, $categorySql);
$categories = [];
while ($categoryRow = mysqli_fetch_assoc($categoryResult)) {
    $category = $categoryRow['科类'];
    if (!$showAllCategories && in_array($category, ["物理类", "历史类"])) {
        $categories[] = $category;
        if (empty($currentCategory)) {
            $currentCategory = $category;
        }
    } elseif ($showAllCategories) {
        $categories[] = $category;
        if (empty($currentCategory)) {
            $currentCategory = $category;
        }
    }
}

// 每个科类的院校信息
$collegeData = [];
$totalRecords = [];
$hasRecords = false;
foreach ($categories as $category) {
    $categoryStart = ($category === $currentCategory) ? $start : 0;

    if ($showAllCategories) {
        // 以批次查询院校信息，条件为科类等于$gxlb，按院校最低投档线降序排序
        $sql = "SELECT `$tdxk_mc`.*, `yxinfo`.*,
                COUNT(DISTINCT $zsjh_mc.`专业代号`) as 专业数量,
                SUM($zsjh_mc.`计划数`) as 招生计划总数,
                GROUP_CONCAT(DISTINCT $zsjh_mc.`选科要求`) as 选科要求,
                (SELECT MIN(t2.`投档线`) FROM `$tdxk_mc` t2 WHERE t2.`院校名称` = `$tdxk_mc`.`院校名称` AND t2.`批次` = '$category' AND t2.`科类` = '$gxlb') as 院校最低投档线
                FROM `$tdxk_mc`
                JOIN `yxinfo` ON `$tdxk_mc`.`院校名称` = `yxinfo`.`院校名称`
                LEFT JOIN `$zsjh_mc` ON `$zsjh_mc`.`院校代号` = `$tdxk_mc`.`院校代号` AND `$zsjh_mc`.`专业组代号` = `$tdxk_mc`.`专业组代号` AND `$zsjh_mc`.`科类` = '$gxlb'
                WHERE `$tdxk_mc`.`批次` = '$category' AND `$tdxk_mc`.`科类` = '$gxlb'
                GROUP BY `$tdxk_mc`.`院校名称`
                ORDER BY 院校最低投档线 DESC
                LIMIT $categoryStart, $limit";
    } else {
        // 正常查询院校信息，按院校最低投档线降序排序
        $sql = "SELECT `$tdxk_mc`.*, `yxinfo`.*,
                COUNT(DISTINCT $zsjh_mc.`专业代号`) as 专业数量,
                SUM($zsjh_mc.`计划数`) as 招生计划总数,
                GROUP_CONCAT(DISTINCT $zsjh_mc.`选科要求`) as 选科要求,
                (SELECT MIN(t2.`投档线`) FROM `$tdxk_mc` t2 WHERE t2.`院校名称` = `$tdxk_mc`.`院校名称` AND t2.`科类` = '$category' AND $pc) as 院校最低投档线
                FROM `$tdxk_mc`
                JOIN `yxinfo` ON `$tdxk_mc`.`院校名称` = `yxinfo`.`院校名称`
                LEFT JOIN `$zsjh_mc` ON `$zsjh_mc`.`院校代号` = `$tdxk_mc`.`院校代号` AND `$zsjh_mc`.`专业组代号` = `$tdxk_mc`.`专业组代号` AND `$zsjh_mc`.`科类` = '$category'
                WHERE `$tdxk_mc`.`科类` = '$category' AND $pc AND ($if_jh)
                GROUP BY `$tdxk_mc`.`院校名称`
                ORDER BY 院校最低投档线 DESC
                LIMIT $categoryStart, $limit";
    }

    $result = mysqli_query($conn, $sql);
    if (!$result) {
        echo "查询出错: ". mysqli_error($conn);
    }
    $collegeData[$category] = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $collegeData[$category][] = $row;
    }
    if (!empty($collegeData[$category])) {
        $hasRecords = true;
    }

    if (empty($collegeData[$category])) {
        // 避免重复输出提示信息
        continue;
    }

    if ($showAllCategories) {
        // 以批次查询总数，条件为科类等于$gxlb，按院校计数
        $totalSql = "SELECT COUNT(DISTINCT `$tdxk_mc`.`院校名称`) as total
                     FROM `$tdxk_mc`
                     JOIN `yxinfo` ON `$tdxk_mc`.`院校名称` = `yxinfo`.`院校名称`
                     WHERE `$tdxk_mc`.`批次` = '$category' AND `$tdxk_mc`.`科类` = '$gxlb'";
    } else {
        // 正常查询总数，按院校计数
        $totalSql = "SELECT COUNT(DISTINCT `$tdxk_mc`.`院校名称`) as total
                     FROM `$tdxk_mc`
                     JOIN `yxinfo` ON `$tdxk_mc`.`院校名称` = `yxinfo`.`院校名称`
                     WHERE `$tdxk_mc`.`科类` = '$category' AND $pc AND ($if_jh)";
    }
    $totalResult = mysqli_query($conn, $totalSql);
    $totalRow = mysqli_fetch_assoc($totalResult);
    $totalRecords[$category] = $totalRow['total'];
}
?>
<style>

    .layui-tabs-header {
        display: flex;
        justify-content: center;
    }
    .layui-tabs-header li {
        flex: 1;
        text-align: center;
        font-weight: bold;
        font-size: 16px;
    }
    .expand-icon {
        cursor: pointer;
        font-weight: bold;
        color:#16b777;
    }
    .major-list {
        display: none;
        margin-top: 10px;
    }

</style>
<script type="text/javascript" src="./js/jquery.min.js"></script>
<script type="text/javascript" src="./js/layui.js"></script>
<script type="text/javascript" src="./js/myjs.js"></script>
<body>
    <div class="container">
        <?php if (!$hasRecords): ?>
            <div style="text-align: center; padding: 60px 20px; background: #f8f9fa; border-radius: 8px; margin: 20px 0;">
                <div style="font-size: 48px; color: #e0e0e0; margin-bottom: 20px;">
                    <i class="layui-icon layui-icon-search"></i>
                </div>
                <h3 style="color: #666; margin-bottom: 10px; font-weight: normal;">暂无符合条件的院校</h3>
                <p style="color: #999; font-size: 14px; line-height: 1.6;">
                    请尝试：<br>
                    • 调整筛选条件<br>
                    • 选择其他院校类别<br>
                    • 检查是否有相关数据
                </p>
                <div style="margin-top: 20px;">
                    <button class="layui-btn layui-btn-primary" onclick="history.back()">
                        <i class="layui-icon layui-icon-return"></i> 返回上一页
                    </button>
                </div>
            </div>
        <?php else: ?>
            <div class="layui-tabs" lay-options="{index: 0}">
                <ul class="layui-tabs-header">
                    <?php foreach ($categories as $index => $category): ?>
                        <li <?php if ($category === $currentCategory) echo 'class="layui-this"'; ?>><?php echo $category; ?></li>
                    <?php endforeach; ?>
                </ul>
                <div class="layui-tabs-body">
                    <?php foreach ($categories as $index => $category): ?>
                        <div class="layui-tabs-item <?php if ($category === $currentCategory) echo 'layui-show'; ?>" data-category="<?php echo $category; ?>">
                            <?php if (!empty($collegeData[$category])): ?>
                                <?php
                                $displayCount = 3;
                                $shouldShowPagination = true;
                                if ($wdl_xz == 1 && empty($dlusername)) {
                                    $displayData = array_slice($collegeData[$category], 0, $displayCount);
                                    if (count($collegeData[$category]) > $displayCount) {
                                        $shouldShowPagination = false;
                                    }
                                } elseif ($wdl_xz == 1 && $dlday_xz == 1 && @$_COOKIE['syday'] <= 0) {
                                    $displayData = array_slice($collegeData[$category], 0, $displayCount);
                                    if (count($collegeData[$category]) > $displayCount) {
                                        $shouldShowPagination = false;
                                    }
                                } else {
                                    $displayData = $collegeData[$category];
                                }
                                $k = 0;
                                foreach ($displayData as $row):
                                    $k++;
                                    // 使用zysx_x_functions.php中的get_hyts_info函数获取行业特色信息
                                    $hytsInfo = get_hyts_info($conn, $row['院校名称']);

                                    // 获取近三年投档表信息
                                    $recent_tables = get_recent_toudang_tables($conn, 3);
                                    $recent_years = [];
                                    foreach ($recent_tables as $table) {
                                        if (preg_match('/tdx(\d{4})/', $table, $matches)) {
                                            $recent_years[] = $matches[1];
                                        }
                                    }
                                    ?>
                                    <div class="college-card">
                                        <!-- 行业特色徽章（右上角） -->
                                        <?php if ($hytsInfo && !empty($hytsInfo['行业特色'])): ?>
                                            <span class="college-hyts-badge-zysx" data-hyts="<?php echo htmlspecialchars($hytsInfo['行业特色']); ?>" data-yxtd="<?php echo htmlspecialchars($hytsInfo['院校特点'] ?? ''); ?>"><?php echo $hytsInfo['行业特色']; ?></span>
                                        <?php endif; ?>

                                        <!-- 院校序号徽章（右上角） -->
                                        <span class="college-index-badge-zysx"><?php echo $k; ?></span>

                                        <!-- 院校标题 -->
                                        <div class="college-title">
                                            <a href="showyxxx_xgk.php?id=<?=$row['id']?><?=$bkkllink?>" target="_blank"><?php echo $row['院校名称']; ?></a>
                                        </div>

                                        <!-- 院校特征标签 - 使用zysx_x_api.php中的算法 -->
                                        <?php
                                        // 构建院校特征信息，参照zysx_x_api.php中的build_college_feature函数
                                        $college_features = [];
                                        // 只显示985，不显示211
                                        if (isset($row['f985']) && intval($row['f985']) == 1) {
                                            $college_features[] = '985';
                                        } elseif (isset($row['f211']) && intval($row['f211']) == 1) {
                                            $college_features[] = '211';
                                        }
                                        if (!empty($row['双一流'])) $college_features[] = $row['双一流'];
                                        if ($row['中央部属'] == '1') $college_features[] = '部委直属';
                                        if (!empty($row['学院层次'])) {
                                            if (strpos($row['学院层次'], '76001') !== false) $college_features[] = '国家示范性高职院校';
                                            if (strpos($row['学院层次'], '76002') !== false) $college_features[] = '国家骨干高职院校';
                                        }
                                        if (isset($row['双高计划']) && $row['双高计划'] !== '' && $row['双高计划'] !== '0') $college_features[] = '双高计划';

                                        // 显示特征标签
                                        foreach ($college_features as $feature): ?>
                                            <span class="college-badge"><?php echo $feature; ?></span>
                                        <?php endforeach; ?>

                                        <!-- 院校基本信息 -->
                                        <div class="college-meta">
                                            <span><?php echo $row['省份名称'] . ' • ' . $row['类型'] . ' • ' . $row['层次'] . ' • ' . $row['办学性质']; ?></span>
                                        </div>

                                        <!-- 排名和保研率信息 -->
                                        <div class="college-rank-info">
                                            <?php
                                            // 使用与zysx_x.php相同的方式获取排名数据
                                            $college_ranks = get_college_rank($conn, $row['院校名称']);
                                            $yjsy_rank = get_college_yjsy_rank($conn, $row['院校名称']);

                                            $has_rank = false;

                                            // 处理普通排名
                                            if (!empty($college_ranks)) {
                                                foreach ($college_ranks as $rank) {
                                                    $rank_text = $rank['排名类型'] . ' ' . $rank['排名'];
                                                    if (!empty($rank['分类'])) {
                                                        $rank_text .= '（' . $rank['分类'] . '）';
                                                    }
                                                    echo '<span class="rank-badge">' . $rank_text . '</span>';
                                                    $has_rank = true;
                                                }
                                            }

                                            // 处理研究生院排名
                                            if (!empty($yjsy_rank)) {
                                                echo '<span class="rank-badge">研究生院 ' . $yjsy_rank['名次'] . '</span>';
                                                $has_rank = true;
                                            }

                                            // 保研率（如有）
                                            if (!empty($row['保研率']) && $row['保研率'] > 0) {
                                                $baoyanlv = number_format($row['保研率'], 2);
                                                $baoyanlv = rtrim(rtrim($baoyanlv, '0'), '.');
                                                echo '<span class="rank-badge">保研率 ' . $baoyanlv . '%</span>';
                                                $has_rank = true;
                                            }

                                            if (!$has_rank) {
                                                echo '<span class="rank-badge" style="color: #999;">暂无排名信息</span>';
                                            }
                                            ?>
                                        </div>

                                        <!-- 投档信息 - 按年份显示近三年 -->
                                        <?php
                                        $found_toudang = false;
                                        foreach ($recent_years as $year):
                                            // 使用zysx_x_functions.php中的get_toudang_stats函数获取投档统计信息
                                            $toudang_stats = get_toudang_stats($conn, $row['院校名称'], $row['科类'], $row['批次'], $year);

                                            if ($toudang_stats):
                                                $found_toudang = true;
                                                // 使用zysx_x_functions.php中的get_luqu_total函数获取录取总数
                                                $luqu_total = get_luqu_total($conn, $row['院校名称'], $row['科类'], $row['批次'], $year);

                                                $score_text = $toudang_stats['投档分文本'];
                                                $rank_text = $toudang_stats['排名文本'];
                                                ?>
                                                <div class="college-rank toudang-batch-title" style="position:relative;cursor:pointer;display:flex;align-items:flex-start;min-height:40px;"
                                                     onclick="showToudangDetail('<?php echo urlencode($row['院校名称']); ?>', '<?php echo urlencode($row['科类']); ?>', '<?php echo urlencode($row['批次']); ?>', '<?php echo $year; ?>')">
                                                    <div style="flex:1;">
                                                        <div>
                                                            <b style="color:#16baaa;">投档信息（<?php echo $year; ?>）</b>
                                                            <span style="margin-left:5px;">录取总数：<b><?php echo $luqu_total > 0 ? $luqu_total : '暂无'; ?></b></span>
                                                        </div>
                                                        <div style="margin-top:2px;">
                                                            <?php echo $score_text; ?>
                                                            <?php if ($rank_text): ?>
                                                                &nbsp;&nbsp;<?php echo $rank_text; ?>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                    <span class="arrow-span" style="align-self:center;"><i class="layui-icon layui-icon-right"></i></span>
                                                </div>
                                                <?php
                                            endif;
                                        endforeach;

                                        if (!$found_toudang): ?>
                                            <div class="college-rank"><b>投档信息：</b>暂无投档信息</div>
                                        <?php endif; ?>

                                        <!-- 招生计划 -->
                                        <div class="zsjh-batch-title" style="position:relative;cursor:pointer;display:flex;align-items:center;"
                                             onclick="showZsjhDetail('<?php echo urlencode($row['院校名称']); ?>', '<?php echo urlencode($row['科类']); ?>', '<?php echo urlencode($row['批次']); ?>', '<?php echo $tdxk_nf; ?>')">
                                            <div style="flex:1;">
                                                <b style="color:#16baaa;">招生计划（<?php echo $tdxk_nf; ?>）</b>
                                                <span style="margin-left:5px;">计划总数：<b><?php echo $row['招生计划总数'] ?? '暂无'; ?></b></span>
                                            </div>
                                            <span class="arrow-span" style="align-self:center;"><i class="layui-icon layui-icon-right"></i></span>
                                        </div>
                                    </div>
                                <?php endforeach;
                                if ($wdl_xz == 1 && empty($dlusername) && count($collegeData[$category]) > $displayCount) {
                                    echo '<div style="text-align:center;font-size:16px;color:#a0a0a0;line-height:40px;"><a href="login.php" style="color:#3eb1dd;">登录</a>后显示更多院校信息！</div>';
                                } elseif ($wdl_xz == 1 && $dlday_xz == 1 && @$_COOKIE['syday'] <= 0 && count($collegeData[$category]) > $displayCount) {
                                    echo '<div style="text-align:center;font-size:16px;color:#a0a0a0;line-height:40px;"><a href="word_pay.php" style="color:#ff0000;">会员到期</a>，<a href="word_pay.php" style="color:#3eb1dd;">充值</a>后显示更多信息！</div>';
                                }
                                if ($shouldShowPagination):
                                ?>
                                    <center>
                                        <div class="layui-laypage" id="pagination-<?php echo str_replace([' ', '/', '\\'], '-', $category); ?>"></div>
                                    </center>
                                <?php endif; ?>
                            <?php else: ?>
                                <p>该科类（<?php echo $category; ?>）下没有院校信息。</p>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <script>
        // 显示投档信息详情弹窗（全局函数）
        function showToudangDetail(college, type, batch, year) {
            var url = 'zysx_x_toudang_detail.php?college=' + college + '&type=' + type + '&batch=' + batch + '&year=' + year;
            layer.open({
                type: 2,
                title: false,
                area: ['100%', '100%'],
                move: false,
                anim: 2,
                offset: '0px',
                shadeClose: true,
                scrollbar: false,
                closeBtn: 1,
                content: url,
                success: function(layero, index) {
                    var $closeBtn = layero.find('.layui-layer-close');
                    if ($closeBtn.length) {
                        $closeBtn.css({
                            'position': 'absolute',
                            'top': '10px',
                            'right': '10px',
                            'z-index': '999999',
                            'width': '30px',
                            'height': '30px',
                            'border-radius': '50%',
                            'display': 'flex',
                            'align-items': 'center',
                            'justify-content': 'center',
                            'color': '#fff',
                            'font-size': '16px',
                            'cursor': 'pointer'
                        });
                    }
                }
            });
        }

        // 显示招生计划详情弹窗（全局函数）
        function showZsjhDetail(college, type, batch, year) {
            var url = 'zysx_x_zsjh_detail.php?college=' + college + '&type=' + type + '&batch=' + batch + '&year=' + year;
            layer.open({
                type: 2,
                title: false,
                area: ['100%', '100%'],
                move: false,
                anim: 2,
                offset: '0px',
                shadeClose: true,
                scrollbar: false,
                closeBtn: 1,
                content: url,
                success: function(layero, index) {
                    var $closeBtn = layero.find('.layui-layer-close');
                    if ($closeBtn.length) {
                        $closeBtn.css({
                            'position': 'absolute',
                            'top': '10px',
                            'right': '10px',
                            'z-index': '999999',
                            'width': '30px',
                            'height': '30px',
                            'border-radius': '50%',
                            'display': 'flex',
                            'align-items': 'center',
                            'justify-content': 'center',
                            'color': '#fff',
                            'font-size': '16px',
                            'cursor': 'pointer'
                        });
                    }
                }
            });
        }

        // 行业特色徽章点击弹窗
        $(document).on('click', '.college-hyts-badge-zysx', function() {
            var hyts = decodeURIComponent($(this).data('hyts') || '');
            var yxtd = decodeURIComponent($(this).data('yxtd') || '');
            var content = '';
            if (hyts) content += '<div style="margin-bottom:8px;"><b>行业特色：</b>' + hyts + '</div>';
            if (yxtd) content += '<div style="white-space:pre-line;"><b>院校特点：</b>' + yxtd + '</div>';
            if (!content) content = '<span style="color:#888;">暂无行业特色和院校特点信息</span>';
            layer.open({
                type: 1,
                title: '',
                area: ['90%', 'auto'],
                shadeClose: true,
                content: '<div style="padding:15px;">' + content + '</div>'
            });
        });

        layui.use('laypage', function () {
            var laypage = layui.laypage;
            var totalRecords = <?php echo json_encode($totalRecords); ?>;
            var limit = <?php echo $limit; ?>;
            var gxlb = '<?php echo $gxlb; ?>';
            var currentCategory = '<?php echo $currentCategory; ?>';

            function renderPagination(category, page) {
                var total = totalRecords[category];
                var containerId = 'pagination-' + category.replace(/[ /\\]/g, '-');
                if (total > 0) {
                    laypage.render({
                        elem: containerId,
                        count: total,
                        limit: limit,
                        curr: page || 1,
                        layout: ['prev', 'page', 'next', 'count', 'skip'],
                        groups: 1,
                        jump: function (obj, first) {
                            if (!first) {
                                var url = window.location.href.split('?')[0];
                                var params = {
                                    gxlb: gxlb,
                                    page: obj.curr,
                                    category: category
                                };
                                var queryString = Object.keys(params).map(key => key + '=' + params[key]).join('&');
                                window.location.href = url + '?' + queryString;
                            }
                        }
                    });
                }
            }

            // 初始化分页
            if (<?php echo json_encode($shouldShowPagination); ?>) {
                renderPagination(currentCategory, <?php echo $page; ?>);
            }

            // 监听标签切换事件
            $('.layui-tabs-header li').on('click', function (e) {
                e.preventDefault();
                var index = $(this).index();
                var category = '<?php echo implode(',', $categories); ?>'.split(',')[index];
                currentCategory = category;
                // 移除所有标签的激活状态
                $('.layui-tabs-header li').removeClass('layui-this');
                $('.layui-tabs-item').removeClass('layui-show');
                // 激活当前标签
                $(this).addClass('layui-this');
                $('[data-category="' + category + '"]').addClass('layui-show');
                if (<?php echo json_encode($shouldShowPagination); ?>) {
                    renderPagination(category, 1);
                }
            });

            // 确保页面加载时显示正确的标签页和分页
            $(document).ready(function () {
                $('.layui-tabs-header li').removeClass('layui-this');
                $('.layui-tabs-item').removeClass('layui-show');
                var currentTab = $('[data-category="' + currentCategory + '"]');
                var currentTabHeader = $('.layui-tabs-header li:contains("' + currentCategory + '")');
                currentTabHeader.addClass('layui-this');
                currentTab.addClass('layui-show');
                if (<?php echo json_encode($shouldShowPagination); ?>) {
                    renderPagination(currentCategory, <?php echo $page; ?>);
                }
            });

            // 修正翻页时激活标签和内容显示
            function updateActiveTabAndContent(category) {
                $('.layui-tabs-header li').removeClass('layui-this');
                $('.layui-tabs-item').removeClass('layui-show');
                var currentTab = $('[data-category="' + category + '"]');
                var currentTabHeader = $('.layui-tabs-header li:contains("' + category + '")');
                currentTabHeader.addClass('layui-this');
                currentTab.addClass('layui-show');
            }

            // 在分页跳转时更新激活标签和内容
            $('.layui-laypage a').on('click', function (e) {
                var targetPage = $(this).attr('lay-page');
                var currentContainer = $(this).closest('.layui-laypage');
                var category = currentContainer.attr('id').replace('pagination-', '').replace(/-/g, ' ');
                currentCategory = category;
                updateActiveTabAndContent(category);
                if (<?php echo json_encode($shouldShowPagination); ?>) {
                    renderPagination(category, targetPage);
                }
            });
        });

        function loadMajorList(icon, yxdm, zydh, kl) {
            var listId = 'major-list-' + yxdm + '-' + zydh + '-' + kl;
            var list = $('#' + listId);
            
            // 如果已经加载过，直接切换显示/隐藏
            if (list.data('loaded')) {
                toggleMajorList(icon, listId);
                return;
            }
            
            // 显示加载中状态
            list.html('<div class="layui-text" style="padding: 15px; text-align: center;"><i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate"></i> 加载中...</div>');
            list.show();
            $(icon).removeClass('layui-icon-down').addClass('layui-icon-up');
            
            // AJAX请求专业组数据
            $.ajax({
                url: 'cz_nohead.php?cz=get_major_list',
                type: 'GET',
                data: {
                    yxdm: yxdm,
                    zydh: zydh,
                    kl: kl,
                    nf: '<?php echo $tdxk_nf; ?>'
                },
                success: function(response) {
                    list.html(response);
                    list.data('loaded', true);
                    
                    // 关闭其他展开的专业组
                    $('.major-list').not(list).hide();
                    $('.expand-icon').not(icon).removeClass('layui-icon-up').addClass('layui-icon-down');
                },
                error: function() {
                    list.html('<div class="layui-text" style="padding: 15px; color: red;">加载失败，请重试</div>');
                    $(icon).removeClass('layui-icon-up').addClass('layui-icon-down');
                }
            });
        }

        function toggleMajorList(icon, listId) {
            var list = $('#' + listId);
            list.toggle();
            if (list.is(':visible')) {
                $(icon).removeClass('layui-icon-down').addClass('layui-icon-up');
            } else {
                $(icon).removeClass('layui-icon-up').addClass('layui-icon-down');
            }
        }
    </script>
    <?php
    include("dhtool.php");
    ?>
</body>
</html>